---
description:
globs:
alwaysApply: true
---
# 时间获取规则

## 规则概述
当涉及到时间获取需求时，使用本地系统时间和JavaScript运行时进行对比验证，确保时间的准确性。

## 获取方式

### 1. 本地系统时间
```bash
# 获取本地时间
date

# 获取UTC时间
date -u
```

### 2. JavaScript运行时验证
```bash
node -e "
console.log('当前时间:', new Date().toLocaleString('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  weekday: 'long'
}));
console.log('UTC时间:', new Date().toISOString());
console.log('时间戳:', Date.now());
"
```

## 验证要求

### 必须包含的信息
1. **中国本地时间**（CST UTC+8）
2. **时间戳**
3. **本地时间与JS时间对比结果**

### 输出格式
```
## 📅 当前时间

**YYYY年MM月DD日 星期X HH:mm分** (CST UTC+8)
**时间戳：[timestamp]**

### 验证结果
- 本地系统时间：[系统时间]
- JavaScript时间：[JS时间]
- 一致性：[一致/存在差异]
```

## 应用场景
- 任何需要获取当前时间的请求
- 时间相关的功能开发
- 日志记录和时间戳生成
- 时区转换和时间计算

## 注意事项
1. 使用本地系统时间和JavaScript时间进行对比
2. 如有差异，说明可能的原因
3. 优先使用系统时间作为基准
