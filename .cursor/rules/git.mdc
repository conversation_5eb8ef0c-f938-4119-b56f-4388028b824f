---
description: Git conventions and workflow guidelines using Conventional Commits
globs:
alwaysApply: false
---
---
name: git-rules
description: Git提交和版本控制规范
---

# Git 提交信息规范

为了保持 Git 提交历史的一致性和可读性，我们遵循 Conventional Commits 规范。

**⚠️ 重要提醒：在使用AI生成git commit信息时，必须严格遵守以下规范！**

## 格式

每次提交信息都遵循以下格式：

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

-   **header**: 包含 `type`、`scope`（可选）和 `subject`。
    -   `type`: 描述提交的类别。
    -   `scope`: 描述提交影响的范围（可选）。
    -   `subject`: 提交目的的简短描述。
-   **body**: 对提交的详细描述（可选）。
-   **footer**: 包含关联的 issue 和破坏性变更信息（可选）。

---

## Type (类型)

**必须是以下类型之一，生成commit时严格按照此表选择：**

| 类型         | 表情 | 描述                                                     | 使用场景示例 |
| :----------- | :--- | :------------------------------------------------------- | :----------- |
| `feat`       | 🚀   | 新增功能                                                 | 添加新的组件、新的API接口、新的业务功能 |
| `fix`        | 🧩   | 修复缺陷                                                 | 修复bug、修复样式问题、修复逻辑错误 |
| `docs`       | 📚   | 文档变更                                                 | 更新README、添加注释、API文档更新 |
| `style`      | 🎨   | 代码格式（不影响功能，例如空格、分号等格式修正）       | 代码格式化、去除多余空格、统一代码风格 |
| `refactor`   | ♻️   | 代码重构（不包括 bug 修复、功能新增）                    | 组件拆分、Hook提取、代码结构优化 |
| `perf`       | ⚡️   | 性能优化                                                 | 优化算法、减少渲染次数、资源优化 |
| `test`       | ✅   | 添加疏漏测试或已有测试改动                               | 添加单元测试、修改测试用例 |
| `chore`      | 📦️   | 构建流程、外部依赖变更（如升级 npm 包、修改配置等）      | 更新依赖、修改构建配置、环境配置 |
| `ci`         | 🎡   | 修改 CI 配置、脚本                                       | 修改GitHub Actions、Jenkins配置 |
| `revert`     | ⏪️   | 回滚 commit                                              | 撤销之前的提交 |
| `build`      | 🔨   | 项目打包发布                                             | 构建生产版本、发布准备 |
| `wip`        |      | 工作进行中 (Work in progress)                            | 功能开发中的临时提交 |
| `workflow`   |      | 工作流相关文件修改                                       | 修改工作流配置文件 |
| `types`      |      | 类型定义文件修改                                         | 更新TypeScript类型定义 |
| `release`    |      | 发布版本                                                 | 版本发布相关的提交 |

---

## Scope (范围)

-   用于说明 commit 影响的范围，例如组件、功能模块等。
-   范围是**可选的**。
-   允许使用**自定义范围**。
-   **常用范围示例**：
    -   `components`: 组件相关
    -   `hooks`: Hook相关
    -   `utils`: 工具函数相关
    -   `api`: API接口相关
    -   `styles`: 样式相关
    -   `config`: 配置相关
    -   `deps`: 依赖相关

---

## Subject (主题)

-   是对提交目的的简短描述。
-   **不能为空**。
-   `header` (type + scope + subject) 的最大长度为 **108** 个字符。
-   主题首字母**不需要**大写。
-   **使用中文描述**，简洁明了。

---

## Body (正文)

-   对提交的详细描述，可以分成多行。
-   **可选**。
-   与 `subject` 之间必须**空一行** (`body-leading-blank`)。
-   可以使用 `|` 字符进行换行。
-   **应该包含**：
    -   变更的原因
    -   与之前行为的对比
    -   影响范围说明

---

## Footer (页脚)

-   用于记录**破坏性变更**或**关联 Issue**。
-   **可选**。
-   与 `body` 之间必须**空一行** (`footer-leading-blank`)。
-   **关联 Issue**:
    -   可以使用预设前缀 `closed` 或自定义前缀。
    -   格式如: `closed #31`, `#I3244`。
-   **破坏性变更 (Breaking Changes)**:
    -   以 `BREAKING CHANGE:` 开头，后跟破坏性变更的描述。
    -   `feat` 和 `fix` 类型的提交允许包含破坏性变更。

---

## 🚀 AI生成Commit规范

**当使用AI生成git commit信息时，必须遵循以下要求：**

### 1. 严格格式要求
- **必须**使用上述Type类型，不得自创类型
- **必须**遵循 `type(scope): subject` 格式
- **必须**使用中文描述subject
- **必须**确保header长度不超过108个字符

### 2. 内容要求
- **准确描述**变更内容，不得模糊或错误
- **选择合适**的type类型，如：
  - 组件拆分使用 `refactor`
  - 新增功能使用 `feat`
  - 修复问题使用 `fix`
  - 文档更新使用 `docs`

### 3. 示例格式
```bash
# 正确示例
refactor(exercise): 将AudioRecorder组件拆分为useAudioRecorder Hook

将原有的AudioRecorder.vue组件重构为useAudioRecorder Hook，
提高代码复用性和可测试性。

- 移除AudioRecorder.vue组件
- 新增useAudioRecorder.ts Hook
- 更新主组件使用新的Hook
- 减少主文件代码量61%

# 错误示例（不要这样写）
update: 更新代码  # type错误，描述不清晰
feat: Add new feature  # 应该使用中文
refactor: 重构了一些东西  # 描述过于模糊
```

### 4. 质量检查
生成commit信息后，请检查：
- [ ] Type类型是否正确
- [ ] Scope是否合适（可选）
- [ ] Subject描述是否清晰
- [ ] 格式是否符合规范
- [ ] 长度是否超限

---

## 其他规则

-   **忽略的提交**: 提交信息为 `init` 的 commit 会被忽略检查。
-   **分支命名**: 建议使用 `feature/功能名`、`fix/问题描述`、`refactor/重构内容` 等格式。
-   **提交频率**: 建议小步快跑，每个逻辑完整的变更都应该有一个commit。

---

## 📋 Commit模板

为了帮助生成规范的commit信息，可以参考以下模板：

```bash
# 功能开发
feat(scope): 新增XXX功能

详细描述新功能的作用和实现方式

# 问题修复
fix(scope): 修复XXX问题

描述问题的现象和修复方案

# 代码重构
refactor(scope): 重构XXX模块

说明重构的原因和改进效果

# 文档更新
docs(scope): 更新XXX文档

说明文档更新的内容和原因
```

---
