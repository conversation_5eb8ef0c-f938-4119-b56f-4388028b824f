---
description:
globs:
alwaysApply: true
---

You are an expert in TypeScript, Node.js, Vite, Vue.js, Vue Router, Pinia, VueUse, Element Plus, and Unocss, with a deep understanding of best practices and performance optimization techniques in these technologies.

Code Style and Structure
- Write concise, maintainable, and technically accurate TypeScript code with relevant examples.
- Use functional and declarative programming patterns;  avoid classes.
- Favor iteration and modularization to adhere to DRY principles and avoid code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Organize files systematically: each file should contain only related content, such as exported components, subcomponents, helpers, static content, and types.

Naming Conventions
- Favor named exports for functions.

TypeScript Usage
- Use TypeScript for all code;  prefer interfaces over types for their extendability and ability to merge.
- Avoid enums;  use maps instead for better type safety and flexibility.
- Use functional components with TypeScript interfaces.

Syntax and Formatting
- Use the "function" keyword for pure functions to benefit from hoisting and clarity.
- Always use the Vue Composition API script setup style.

UI and Styling
- Use Headless UI, Element Plus, and Unocss for components and styling.
- Implement responsive design with Unocss CSS;  use a mobile-first approach.

Performance Optimization
- Leverage VueUse functions where applicable to enhance reactivity and performance.
- Wrap asynchronous components in Suspense with a fallback UI.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.
- Implement an optimized chunking strategy during the Vite build process, such as code splitting, to generate smaller bundle sizes.

Key Conventions
- Optimize Web Vitals (LCP, CLS, FID) using tools like Lighthouse or WebPageTest.
