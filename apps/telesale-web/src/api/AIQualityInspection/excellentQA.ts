/*
 * @Date         : 2025-05-06 10:42:52
 * @Description  : 优秀问答库API
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { http } from "../../utils/http";
import baseURL from "../url";

/**
 * 优秀问答状态枚举
 */
export const ExcellentQandaStatus = {
  MARK: "mark", // 已添加
  UNMARK: "unmark", // 待处理
  UNDO: "undo" // 暂不处理
} as const;

/**
 * 优秀问答状态类型
 */
export type ExcellentQandaStatusType =
  (typeof ExcellentQandaStatus)[keyof typeof ExcellentQandaStatus];

// 优秀问答列表查询参数接口
export interface ExcellentQandaQueryParams {
  pages?: number; // 页码
  pageSize?: number; // 每页数量
  keyword?: string; // 关键词
  tagId?: number; // 标签ID
  actionId?: string; // 通话ID
  beginTime?: string; // 开始时间
  endTime?: string; // 结束时间
  humanAppraiseReason?: string; // 人工打分原因 通过/不通过
  aiAppraiseScore?: string; // 机器人打分 A-D
  humanAppraiseScore?: string; // 人工打分 A-D
}

// 优秀问答列表项接口
export interface ExcellentQandaItem {
  question?: string; // 用户问题
  answer?: string; // 销售回答
  createdAt?: string; // 创建时间
  markedAt?: string; // 标记时间
  status?: string; // 是否添加 mark  unmark undo
  aiAnswer?: string; // 机器人回复
  uuid?: string; // 唯一标识
  choose?: string; // mark 选择 answer aiAnswer
}

// 优秀问答实例接口
export interface ExcellentQandaInstance {
  id?: string; // 问答ID
  actionId?: string; // 通话ID
  createdAt?: string; // 创建时间
  finished?: number; // 标记数量
  total?: number; // 总数
  items?: ExcellentQandaItem[]; // 问答列表
  audioURL?: string; // 音频地址
  humanAppraiseReason?: string; // 人工打分原因 通过/不通过
  aiAppraiseScore?: string; // 机器人打分 A-D
  humanAppraiseScore?: string; // 人工打分 A-D
}

// 优秀问答列表响应接口
export interface ExcellentQandaQueryReply {
  items: ExcellentQandaInstance[];
  total: string;
}

// 优秀问答标记参数接口
export interface ExcellentQandaMarkParams {
  items?: ExcellentqandaExcellentQandaItem[];
}

export interface ExcellentqandaExcellentQandaItem {
  /**
   * 机器人回复
   */
  aiAnswer?: string;
  /**
   * 销售回答
   */
  answer?: string;
  /**
   * mark 选择 answer aiAnswer
   */
  choose?: string;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 标记时间
   */
  markedAt?: string;
  /**
   * 用户问题
   */
  question?: string;
  /**
   * 是否添加 mark  unmark
   */
  status?: string;
  /**
   * 唯一标识
   */
  uuid?: string;
  [property: string]: any;
}

// 知识问题实例接口
export interface KnowledgeQuestionInstance {
  question: string; // 问题
  trainId?: string; // 训练ID
  uuid?: string; // 唯一ID
}

// 知识库添加请求接口
export interface KnowledgeAddRequest {
  baseQuestion: string; // 基础问题
  knowledgeCategoryId: number; // 知识分类ID
  answer: string; // 答案
  libraryId: number; // 知识库ID
  effective?: number; // 生效时间
  expire?: number; // 过期时间
  timeStatus?: boolean; // 时间状态
  KnowledgeQuestions?: KnowledgeQuestionInstance[]; // 相似问题列表
  source?: string; // 来源
  tags?: any[]; // 标签列表
}

// 知识库添加响应接口
export interface KnowledgeAddReply {
  // 响应体为空对象
}

// 知识库分类节点信息接口
export interface LibraryCategoryNodeInfo {
  id: string; // 分类ID
  name: string; // 分类名称
  parentCategoryId: string; // 父分类ID
  level: string; // 层级
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  deletedAt?: string; // 删除时间
  nodes?: LibraryCategoryNodeInfo[]; // 子节点
}

// 知识库分类列表响应接口
export interface LibraryCategoryListReply {
  nodes: LibraryCategoryNodeInfo[]; // 分类节点列表
}

// 知识库分类查询参数接口
export interface LibraryCategoryQueryParams {
  libraryId?: number; // 知识库ID
  libraryUUID?: string; // 知识库UUID
}

/**
 * @description: 获取优秀问答列表
 * @param {ExcellentQandaQueryParams} params 查询参数
 * @return {Promise<ExcellentQandaQueryReply>} 优秀问答列表
 */
export function getExcellentQandaList(params: ExcellentQandaQueryParams) {
  return http.get<any, ExcellentQandaQueryReply>(
    `${baseURL.robot}/admin/excellentQanda`,
    {
      params
    }
  );
}

/**
 * @description: 标记优秀问答
 * @param {ExcellentQandaMarkParams} params 标记参数
 * @return {Promise<any>} 标记结果
 */
export function markExcellentQanda(
  id: number,
  params: ExcellentQandaMarkParams
) {
  return http.post(`${baseURL.robot}/admin/excellentQanda/${id}`, {
    data: params
  });
}

/**
 * @description: 添加知识库问答
 * @param {KnowledgeAddRequest} params 添加参数
 * @return {Promise<KnowledgeAddReply>} 添加结果
 */
export function addKnowledge(params: KnowledgeAddRequest) {
  return http.post<any, KnowledgeAddReply>(
    `${baseURL.aisaas}/web/knowledge/add`,
    {
      data: params
    }
  );
}

/**
 * @description: 获取知识库分类列表（管理端接口）
 * @param {number} libraryId 知识库ID
 * @return {Promise<LibraryCategoryListReply>} 分类列表
 */
export function getLibraryCategoryList(libraryId?: number) {
  return http.get<any, LibraryCategoryListReply>(
    `${baseURL.aisaas}/admin/libraryCategory`,
    {
      params: {
        libraryId
      }
    }
  );
}

/**
 * @description: 查询知识库分类（Web端接口）
 * @param {LibraryCategoryQueryParams} params 查询参数
 * @return {Promise<LibraryCategoryListReply>} 分类列表
 */
export function queryLibraryCategory(params: LibraryCategoryQueryParams) {
  return http.get<any, LibraryCategoryListReply>(
    `${baseURL.aisaas}/web/libraryCategory/query`,
    {
      params
    }
  );
}
