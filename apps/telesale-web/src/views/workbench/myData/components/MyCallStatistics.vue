<!--
 * @Date         : 2025-07-21
 * @Description  : 我的通话情况统计
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import CallStatisticsChart from "./chat/CallStatisticsChart.vue";
import TimeFilter from "./TimeFilter.vue";

const time = ref();
const loading = ref(false);

const changeTime = () => {
  console.log("time", time.value);
  getData();
};

const getData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 统计数据
const statisticsData = ref({
  dialLeads: { value: 1397, rate: 32.4, isUp: false, target: 1010 },
  dialTimes: { value: 2001, rate: 32.4, isUp: true, target: 0 },
  effectiveCalls: { value: 625, rate: 32.4, isUp: false, target: 504 },
  effectiveDuration: {
    value: "10小时47分31秒",
    rate: 32.4,
    isUp: true,
    target: "13时09分15秒"
  }
});

getData();
</script>

<template>
  <el-card v-loading="loading">
    <div class="flex justify-end">
      <TimeFilter v-model="time" @change="changeTime" />
    </div>

    <el-row :gutter="20">
      <!-- 图表区域 -->
      <el-col :span="18">
        <CallStatisticsChart />
      </el-col>

      <!-- 统计数据区域 -->
      <el-col :span="6">
        <div class="statistics-panel">
          <!-- 拨打线索数 -->
          <div class="stat-item">
            <div class="stat-label">拨打线索数</div>
            <div class="stat-value">{{ statisticsData.dialLeads.value }}</div>
            <div
              class="stat-rate"
              :class="{
                'rate-up': !statisticsData.dialLeads.isUp,
                'rate-down': statisticsData.dialLeads.isUp
              }"
            >
              <span class="rate-icon">
                {{ statisticsData.dialLeads.isUp ? "↑" : "↓" }}
              </span>
              {{ statisticsData.dialLeads.rate }}% 较上个周期
            </div>
            <div class="stat-target">
              目标：{{ statisticsData.dialLeads.target }}
            </div>
          </div>

          <!-- 拨打次数 -->
          <div class="stat-item">
            <div class="stat-label">拨打次数</div>
            <div class="stat-value">{{ statisticsData.dialTimes.value }}</div>
            <div
              class="stat-rate"
              :class="{
                'rate-up': statisticsData.dialTimes.isUp,
                'rate-down': !statisticsData.dialTimes.isUp
              }"
            >
              <span class="rate-icon">
                {{ statisticsData.dialTimes.isUp ? "↑" : "↓" }}
              </span>
              {{ statisticsData.dialTimes.rate }}% 较上个周期
            </div>
          </div>

          <!-- 有效通话量 -->
          <div class="stat-item">
            <div class="stat-label">有效通话量</div>
            <div class="stat-value">
              {{ statisticsData.effectiveCalls.value }}
            </div>
            <div
              class="stat-rate"
              :class="{
                'rate-up': !statisticsData.effectiveCalls.isUp,
                'rate-down': statisticsData.effectiveCalls.isUp
              }"
            >
              <span class="rate-icon">
                {{ statisticsData.effectiveCalls.isUp ? "↑" : "↓" }}
              </span>
              {{ statisticsData.effectiveCalls.rate }}% 较上个周期
            </div>
            <div class="stat-target">
              目标：{{ statisticsData.effectiveCalls.target }}
            </div>
          </div>

          <!-- 有效通话时长 -->
          <div class="stat-item">
            <div class="stat-label">有效通话时长</div>
            <div class="stat-value">
              {{ statisticsData.effectiveDuration.value }}
            </div>
            <div
              class="stat-rate"
              :class="{
                'rate-up': statisticsData.effectiveDuration.isUp,
                'rate-down': !statisticsData.effectiveDuration.isUp
              }"
            >
              <span class="rate-icon">
                {{ statisticsData.effectiveDuration.isUp ? "↑" : "↓" }}
              </span>
              {{ statisticsData.effectiveDuration.rate }}% 较上个周期
            </div>
            <div class="stat-target">
              目标：{{ statisticsData.effectiveDuration.target }}
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </el-card>
</template>

<style lang="scss" scoped>
.statistics-panel {
  padding: 10px;

  .stat-item {
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .stat-rate {
      font-size: 12px;
      margin-bottom: 4px;

      .rate-icon {
        margin-right: 4px;
      }

      &.rate-up {
        color: #52c41a;
      }

      &.rate-down {
        color: #f5222d;
      }
    }

    .stat-target {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
