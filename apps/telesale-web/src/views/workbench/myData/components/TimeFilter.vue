<!--
 * @Date         : 2025-07-18
 * @Description  : 时间筛选组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import dayjs from "dayjs";

interface TimeFilterProps {
  modelValue?: {
    dimension: "week" | "month" | "year";
    time: string;
  };
}

interface TimeFilterEmits {
  (
    e: "update:modelValue",
    value: { dimension: "week" | "month" | "year"; time: string }
  ): void;
  (
    e: "change",
    value: { dimension: "week" | "month" | "year"; time: string }
  ): void;
}

const props = withDefaults(defineProps<TimeFilterProps>(), {
  modelValue: () => ({
    dimension: "month",
    time: dayjs().format("YYYY-MM")
  })
});

const emit = defineEmits<TimeFilterEmits>();

// 维度选项
const dimensionOptions = [
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "年", value: "year" }
];

// 当前选择的维度和时间
const currentDimension = ref(props.modelValue.dimension);
const currentTime = ref(props.modelValue.time);

// 根据维度获取时间选择器的类型和格式
const timePickerConfig = computed(() => {
  switch (currentDimension.value) {
    case "week":
      return {
        type: "week",
        format: "YYYY [第] ww [周]",
        valueFormat: "YYYY-MM-DD",
        placeholder: "选择周"
      };
    case "month":
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
    case "year":
      return {
        type: "year",
        format: "YYYY",
        valueFormat: "YYYY",
        placeholder: "选择年份"
      };
    default:
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
  }
});

// 维度变化时重置时间
const handleDimensionChange = () => {
  let newTime = "";
  switch (currentDimension.value) {
    case "week":
      newTime = dayjs().startOf("week").add(1, "day").format("YYYY-MM-DD");
      break;
    case "month":
      newTime = dayjs().format("YYYY-MM");
      break;
    case "year":
      newTime = dayjs().format("YYYY");
      break;
  }
  currentTime.value = newTime;
  emitChange();
};

// 时间变化
const handleTimeChange = () => {
  emitChange();
};

// 发送变化事件
const emitChange = () => {
  const value = {
    dimension: currentDimension.value,
    time: currentTime.value
  };
  emit("update:modelValue", value);
  emit("change", value);
};

// 监听props变化
watch(
  () => props.modelValue,
  newVal => {
    currentDimension.value = newVal.dimension;
    currentTime.value = newVal.time;
  },
  { deep: true }
);
</script>

<template>
  <div class="time-filter flex items-center gap-10px">
    <!-- 维度选择 -->
    <el-select
      v-model="currentDimension"
      @change="handleDimensionChange"
      style="width: 80px"
    >
      <el-option
        v-for="item in dimensionOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>

    <!-- 时间选择 -->
    <el-date-picker
      v-model="currentTime"
      :type="timePickerConfig.type"
      :format="timePickerConfig.format"
      :value-format="timePickerConfig.valueFormat"
      :placeholder="timePickerConfig.placeholder"
      :clearable="false"
      style="width: 180px"
      @change="handleTimeChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.time-filter {
  .el-radio-group {
    .el-radio-button {
      --el-radio-button-checked-bg-color: #409eff;
      --el-radio-button-checked-text-color: #fff;
    }
  }
}
</style>
