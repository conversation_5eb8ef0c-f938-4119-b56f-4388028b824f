<!--
 * @Date         : 2025-03-28 17:23:01
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import dayjs from "dayjs";
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

const props = defineProps<{
  orgId: number;
}>();

const loading = ref(false);
const month = ref(dayjs().format("YYYY-MM"));
const option: ECOption = {
  xAxis: {
    type: "category",
    data: ["Mon", "Tue", "Wed", "<PERSON>hu", "Fri", "Sat", "Sun"]
  },
  yAxis: {
    type: "value"
  },
  series: [
    {
      data: [120, 200, 150, 80, 70, 110, 130],
      type: "bar",
      itemStyle: {
        color: "#3f9eff"
      }
    }
  ]
};

const getData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

watch(
  () => props.orgId,
  () => {
    getData();
  }
);
</script>

<template>
  <el-card v-loading="loading" class="h-530px">
    <div class="flex justify-between items-center">
      <span class="font-bold text-16px">月度营收达标情况</span>
      <el-date-picker
        v-model="month"
        type="month"
        style="width: 180px"
        value-format="YYYY-MM"
        :clearable="false"
        @change="getData"
      />
    </div>
    <div class="c-#409EFF flex justify-between my-20px">
      <span>月度营收 20000</span>
      <span>月目标 20000</span>
      <span>进度 30.5</span>
    </div>
    <el-progress :text-inside="true" :stroke-width="16" :percentage="70" />
    <Echarts :option="option" height="400" />
  </el-card>
</template>

<style scoped></style>
