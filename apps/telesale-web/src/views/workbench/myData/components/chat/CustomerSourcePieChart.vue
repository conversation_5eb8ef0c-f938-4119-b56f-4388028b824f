<!--
 * @Date         : 2025-07-21
 * @Description  : 已成交客户池来源饼图
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

interface PieData {
  name: string;
  value: number;
  percentage?: number;
}

interface CustomerSourcePieChartProps {
  loading?: boolean;
  data?: PieData[];
  title?: string;
}

const props = withDefaults(defineProps<CustomerSourcePieChartProps>(), {
  loading: false,
  data: () => [],
  title: "已成交客户池来源"
});

// 模拟数据（扩展到12条数据）
const mockData = [
  { name: "客服转接", value: 45 },
  { name: "电话营销", value: 25 },
  { name: "网络营销", value: 15 },
  { name: "客户自主申请", value: 8 },
  { name: "其他", value: 2 }
];

// 使用传入的数据或模拟数据
const chartData = computed(() => {
  const data = props.data.length > 0 ? props.data : mockData;
  // 计算百分比
  const total = data.reduce((sum, item) => sum + item.value, 0);
  return data.map(item => ({
    ...item,
    percentage: total > 0 ? Math.round((item.value / total) * 100) : 0
  }));
});

// 颜色配置 - 使用多彩随机颜色（12种颜色）
const colors = [
  "#FF6B6B", // 珊瑚红
  "#4ECDC4", // 青绿色
  "#45B7D1", // 天蓝色
  "#96CEB4", // 薄荷绿
  "#FFEAA7" // 柠檬黄
];

// ECharts配置
const option = computed<ECOption>(() => {
  return {
    title: {
      text: props.title,
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      }
    },
    tooltip: {
      trigger: "item",
      formatter: (params: any) => {
        return `${params.name}<br/>数量: ${params.value}<br/>占比: ${params.percent}%`;
      }
    },
    legend: {
      orient: "vertical",
      right: "1%",
      top: "middle",
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12
      },
      formatter: (name: string) => {
        const item = chartData.value.find(d => d.name === name);
        return `${name} ${item?.percentage || 0}%`;
      }
    },
    color: colors,
    series: [
      {
        name: "客户来源",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["40%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: "#fff",
          borderWidth: 2
        },
        label: {
          show: true,
          position: "outside",
          fontSize: 12,
          fontWeight: "normal",
          color: "#333",
          formatter: (params: any) => {
            return `${params.name}\n${params.percent}%`;
          },
          rich: {
            name: {
              fontSize: 12,
              fontWeight: "bold",
              color: "#333"
            },
            percent: {
              fontSize: 11,
              color: "#666"
            }
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: "#999",
            width: 1
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: "bold"
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        data: chartData.value.map(item => ({
          name: item.name,
          value: item.value
        }))
      }
    ]
  };
});
</script>

<template>
  <Echarts :option="option" height="400" />
</template>
