<!--
 * @Date         : 2025-07-21
 * @Description  : 我的客户来源柱状图
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

interface CustomerSourceData {
  name: string;
  value: number;
}

interface CustomerSourceBarChartProps {
  loading?: boolean;
  data?: CustomerSourceData[];
  title?: string;
}

const props = withDefaults(defineProps<CustomerSourceBarChartProps>(), {
  loading: false,
  data: () => [],
  title: "我的客户来源"
});

// 模拟数据（根据图片中的数据）
const mockData = [
  { name: "电话营销", value: 365 },
  { name: "人工分配", value: 219 },
  { name: "客户推荐", value: 45 },
  { name: "线下活动人员", value: 16 },
  { name: "网络营销", value: 434 },
  { name: "其他", value: 105 },
  { name: "客服转接", value: 1258 },
  { name: "自主注册", value: 8 },
  { name: "线下活动", value: 35 },
  { name: "客户自主申请", value: 398 },
  { name: "老客户介绍", value: 9 },
  { name: "其他/未知", value: 56 }
];

// 使用传入的数据或模拟数据
const chartData = computed(() =>
  props.data.length > 0 ? props.data : mockData
);

// ECharts配置
const option = computed<ECOption>(() => {
  const xAxisData = chartData.value.map(item => item.name);
  const seriesData = chartData.value.map(item => ({
    value: item.value,
    itemStyle: {
      color: "#1890ff"
    }
  }));

  return {
    title: {
      text: props.title,
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>客户数: ${data.value.toLocaleString()}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "0%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 12,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + "k";
          }
          return value.toString();
        }
      }
    },
    series: [
      {
        name: "客户数",
        type: "bar",
        barWidth: "60%",
        data: seriesData,
        label: {
          show: true,
          position: "top",
          formatter: (params: any) => {
            if (params.value === 0) return "";
            return params.value.toLocaleString();
          },
          fontSize: 10,
          color: "#666"
        }
      }
    ]
  };
});
</script>

<template>
  <Echarts :option="option" height="400" />
</template>
