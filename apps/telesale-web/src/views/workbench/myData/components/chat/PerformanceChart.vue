<!--
 * @Date         : 2025-07-18
 * @Description  : 我的业绩柱状图
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

interface PerformanceData {
  name: string;
  value: number;
}

interface PerformanceChartProps {
  loading?: boolean;
  data?: PerformanceData[];
  title?: string;
}

const props = withDefaults(defineProps<PerformanceChartProps>(), {
  loading: false,
  data: () => [],
  title: "我的业绩"
});

// 模拟数据（实际使用时从props.data获取）
const mockData = [
  { name: "6/1", value: 3135.93 },
  { name: "6/2", value: 12000 },
  { name: "6/3", value: 2900 },
  { name: "6/4", value: 6711.29 },
  { name: "6/5", value: 4500 },
  { name: "6/6", value: 0 },
  { name: "6/7", value: 1600 },
  { name: "6/8", value: 9064 },
  { name: "6/9", value: 9196 },
  { name: "6/10", value: 10796 },
  { name: "6/11", value: 0 },
  { name: "6/12", value: 7577.23 },
  { name: "6/13", value: 6396 },
  { name: "6/14", value: 1896 },
  { name: "6/15", value: 2965.28 },
  { name: "6/16", value: 0 }
];

// 使用传入的数据或模拟数据
const chartData = computed(() =>
  props.data.length > 0 ? props.data : mockData
);

// ECharts配置
const option = computed<ECOption>(() => {
  const xAxisData = chartData.value.map(item => item.name);
  const seriesData = chartData.value.map(item => ({
    value: item.value,
    itemStyle: {
      color: item.value > 0 ? "#1890ff" : "#d9d9d9"
    }
  }));

  return {
    title: {
      text: props.title,
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      formatter: (params: any) => {
        const data = params[0];
        return `${data.name}<br/>业绩: ${data.value.toLocaleString()}`;
      }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "0%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 12
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + "w";
          }
          return value.toString();
        }
      }
    },
    series: [
      {
        name: "业绩",
        type: "bar",
        barWidth: "60%",
        data: seriesData,
        label: {
          show: true,
          position: "top",
          formatter: (params: any) => {
            if (params.value === 0) return "";
            if (params.value >= 10000) {
              return (params.value / 10000).toFixed(1) + "w";
            }
            return params.value.toLocaleString();
          },
          fontSize: 10,
          color: "#666"
        }
      }
    ]
  };
});
</script>

<template>
  <Echarts :option="option" height="400" />
</template>
