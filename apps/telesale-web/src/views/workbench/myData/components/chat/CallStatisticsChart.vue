<!--
 * @Date         : 2025-07-21
 * @Description  : 我的通话情况统计图
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ECOption } from "/@/components/Echarts/config/index";
import Echarts from "/@/components/Echarts/index.vue";

interface CallData {
  name: string;
  dialLeads: number; // 拨打线索数
  dialTimes: number; // 拨打次数
  effectiveCalls: number; // 有效通话量
  effectiveDuration: number; // 有效通话时长(秒)
}

interface CallStatisticsChartProps {
  loading?: boolean;
  data?: CallData[];
  title?: string;
}

const props = withDefaults(defineProps<CallStatisticsChartProps>(), {
  loading: false,
  data: () => [],
  title: "我的通话情况"
});

// 时长格式化函数：秒转换为X天X时X分X秒
const formatDuration = (seconds: number): string => {
  if (seconds === 0) return "0秒";

  const days = Math.floor(seconds / (24 * 3600));
  const hours = Math.floor((seconds % (24 * 3600)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = "";
  if (days > 0) result += `${days}天`;
  if (hours > 0) result += `${hours}时`;
  if (minutes > 0) result += `${minutes}分`;
  if (secs > 0) result += `${secs}秒`;

  return result;
};

// 模拟数据
const mockData: CallData[] = [
  {
    name: "7/1",
    dialLeads: 45,
    dialTimes: 120,
    effectiveCalls: 25,
    effectiveDuration: 1800000
  },
  {
    name: "7/2",
    dialLeads: 52,
    dialTimes: 135,
    effectiveCalls: 30,
    effectiveDuration: 2100222
  },
  {
    name: "7/3",
    dialLeads: 38,
    dialTimes: 98,
    effectiveCalls: 18,
    effectiveDuration: 1350333
  },
  {
    name: "7/4",
    dialLeads: 61,
    dialTimes: 156,
    effectiveCalls: 35,
    effectiveDuration: 2580444
  },
  {
    name: "7/5",
    dialLeads: 43,
    dialTimes: 112,
    effectiveCalls: 22,
    effectiveDuration: 1620222
  },
  {
    name: "7/6",
    dialLeads: 0,
    dialTimes: 0,
    effectiveCalls: 0,
    effectiveDuration: 0
  },
  {
    name: "7/7",
    dialLeads: 29,
    dialTimes: 78,
    effectiveCalls: 15,
    effectiveDuration: 10803122
  },
  {
    name: "7/8",
    dialLeads: 55,
    dialTimes: 142,
    effectiveCalls: 32,
    effectiveDuration: 2340213
  },
  {
    name: "7/9",
    dialLeads: 48,
    dialTimes: 125,
    effectiveCalls: 28,
    effectiveDuration: 2040213
  },
  {
    name: "7/10",
    dialLeads: 59,
    dialTimes: 148,
    effectiveCalls: 34,
    effectiveDuration: 2520313
  },
  {
    name: "7/11",
    dialLeads: 41,
    dialTimes: 105,
    effectiveCalls: 20,
    effectiveDuration: 1500412
  },
  {
    name: "7/12",
    dialLeads: 53,
    dialTimes: 138,
    effectiveCalls: 31,
    effectiveDuration: 2280213
  },
  {
    name: "7/13",
    dialLeads: 46,
    dialTimes: 118,
    effectiveCalls: 26,
    effectiveDuration: 1920123
  },
  {
    name: "7/14",
    dialLeads: 37,
    dialTimes: 95,
    effectiveCalls: 17,
    effectiveDuration: 1260123
  },
  {
    name: "7/15",
    dialLeads: 50,
    dialTimes: 128,
    effectiveCalls: 29,
    effectiveDuration: 2160232
  },
  {
    name: "7/16",
    dialLeads: 44,
    dialTimes: 115,
    effectiveCalls: 24,
    effectiveDuration: 1800231
  }
];

// 使用传入的数据或模拟数据
const chartData = computed(() =>
  props.data.length > 0 ? props.data : mockData
);

// ECharts配置
const option = computed<ECOption>(() => {
  const xAxisData = chartData.value.map(item => item.name);

  return {
    title: {
      text: props.title,
      left: "left",
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#333"
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross"
      },
      formatter: (params: any) => {
        const dataIndex = params[0].dataIndex;
        const data = chartData.value[dataIndex];
        const durationMinutes = (data.effectiveDuration / 60).toFixed(1);
        const durationFormatted = formatDuration(data.effectiveDuration);

        return `
          ${data.name}<br/>
          拨打线索数: ${data.dialLeads}<br/>
          拨打次数: ${data.dialTimes}<br/>
          有效通话量: ${data.effectiveCalls}<br/>
          有效通话时长: ${durationFormatted}
        `;
      }
    },
    legend: {
      data: ["拨打线索数", "拨打次数", "有效通话量", "有效通话时长/min"],
      top: 30
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "25%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        rotate: 45,
        fontSize: 12
      }
    },
    yAxis: [
      {
        type: "value",
        name: "数量",
        position: "left",
        axisLabel: {
          formatter: "{value}"
        }
      },
      {
        type: "value",
        name: "时长(分钟)",
        position: "right",
        axisLabel: {
          formatter: "{value}min"
        }
      }
    ],
    series: [
      {
        name: "拨打线索数",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.map(item => item.dialLeads),
        itemStyle: {
          color: "#1890ff"
        },
        barGap: "0%",
        barCategoryGap: "20%"
      },
      {
        name: "拨打次数",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.map(item => item.dialTimes),
        itemStyle: {
          color: "#52c41a"
        }
      },
      {
        name: "有效通话量",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.map(item => item.effectiveCalls),
        itemStyle: {
          color: "#faad14"
        }
      },
      {
        name: "有效通话时长/min",
        type: "line",
        yAxisIndex: 1,
        data: chartData.value.map(item =>
          parseFloat((item.effectiveDuration / 60).toFixed(1))
        ),
        itemStyle: {
          color: "#f5222d"
        },
        lineStyle: {
          width: 3
        },
        symbol: "circle",
        symbolSize: 8,
        smooth: true
      }
    ]
  };
});
</script>

<template>
  <Echarts :option="option" height="400" />
</template>
