<!--
 * @Date         : 2025-07-21 16:52:28
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import PerformanceChart from "./chat/PerformanceChart.vue";
import PerformancePieChart from "./chat/PerformancePieChart.vue";
import TimeFilter from "./TimeFilter.vue";

const time = ref();
const loading = ref(false);
const changeTime = () => {
  console.log("time", time.value);
  getData();
};

const getData = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};
getData();
</script>

<template>
  <el-card v-loading="loading">
    <div class="flex justify-end">
      <TimeFilter v-model="time" @change="changeTime" />
    </div>
    <el-row>
      <el-col :span="12">
        <PerformanceChart />
      </el-col>
      <el-col :span="12">
        <PerformancePieChart />
      </el-col>
    </el-row>
  </el-card>
</template>

<style lang="scss" scoped></style>
