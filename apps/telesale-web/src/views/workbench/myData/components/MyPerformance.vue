<!--
 * @Date         : 2025-07-17 16:18:29
 * @Description  : 我的业绩
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import dayjs from "dayjs";
import { progressTextMap } from "../data";

const month = ref(dayjs().format("YYYY-MM"));

const progress = ref(0);
const progressText = computed(() => {
  let text = "";
  for (const key in progressTextMap) {
    if (progress.value < Number(key)) {
      break;
    }
    text = progressTextMap[key];
  }
  return text;
});

const getData = () => {
  console.log("1111", month.value);
};
</script>

<template>
  <el-card>
    <div class="w-100% flex justify-between items-center gap-40px">
      <div class="w-180px">
        <el-button type="primary">我的业绩排名：NO.34</el-button>
      </div>
      <div class="flex-1 flex gap-10px justify-center items-center w-100%">
        <template v-if="progress > 0">
          <span class="font-bold">本月业绩：3000</span>
          <div class="flex-1 text-center">
            <div class="c-red">{{ progress }}%</div>
            <el-progress
              :text-inside="true"
              :stroke-width="24"
              :percentage="progress"
            />
            <div class="c-red">{{ progressText }}</div>
          </div>
          <span class="font-bold">本月目标：6000</span>
        </template>
        <div v-else class="c-#409EFF">
          没有预设的终点，才更有无限可能 ——
          从今天起，每一次成交都是惊喜，每一步成长都是答案！
        </div>
      </div>
      <div class="w-180px">
        <el-date-picker
          v-model="month"
          type="month"
          style="width: 180px"
          value-format="YYYY-MM"
          :clearable="false"
          @change="getData"
        />
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped></style>
