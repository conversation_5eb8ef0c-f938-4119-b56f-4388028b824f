# 我的业绩组件说明

## 组件结构

### 1. TimeFilter.vue - 时间筛选组件
支持按周/月/年进行时间筛选的组件。

**Props:**
- `modelValue`: 当前选择的时间筛选值 `{ dimension: 'week' | 'month' | 'year', time: string }`

**Events:**
- `update:modelValue`: 筛选值变化时触发
- `change`: 筛选值变化时触发

**使用示例:**
```vue
<TimeFilter 
  v-model="timeFilter"
  @change="handleTimeFilterChange"
/>
```

### 2. PerformanceChart.vue - 业绩柱状图组件
展示业绩数据的柱状图组件。

**Props:**
- `loading`: 加载状态
- `data`: 业绩数据数组 `{ name: string, value: number }[]`
- `title`: 图表标题

**使用示例:**
```vue
<PerformanceChart 
  :loading="loading"
  :data="performanceData"
  title="我的业绩"
/>
```

### 3. PerformancePieChart.vue - 业绩分类饼图组件
展示业绩分类的饼图组件。

**Props:**
- `loading`: 加载状态
- `data`: 分类数据数组 `{ name: string, value: number }[]`
- `title`: 图表标题

**使用示例:**
```vue
<PerformancePieChart 
  :loading="loading"
  :data="pieData"
  title="业绩分类"
/>
```

### 4. MyPerformance.vue - 主组件
集成了时间筛选、业绩概览、柱状图和饼图的完整组件。

## API接口

### 接口文件: `api/index.ts`

**主要接口:**
- `getPerformanceChartData(params)`: 获取业绩柱状图数据
- `getPerformanceCategoryData(params)`: 获取业绩分类饼图数据
- `getPerformanceOverview(params)`: 获取业绩概览数据

**参数类型:**
```typescript
interface PerformanceParams {
  dimension: 'week' | 'month' | 'year';
  time: string;
  orgId?: number;
}
```

## 数据格式

### 柱状图数据格式
```typescript
interface PerformanceData {
  name: string;  // 时间标签，如 "6/1", "周一", "1月"
  value: number; // 业绩数值
}
```

### 饼图数据格式
```typescript
interface PerformanceCategoryData {
  name: string;       // 分类名称
  value: number;      // 数值
  percentage: number; // 百分比
}
```

### 概览数据格式
```typescript
interface PerformanceOverview {
  currentPerformance: number; // 当前业绩
  targetPerformance: number;  // 目标业绩
  progress: number;           // 完成进度百分比
  ranking: number;            // 排名
}
```

## 使用方法

1. **在页面中引入主组件:**
```vue
<template>
  <div class="g-margin-20">
    <MyPerformance />
  </div>
</template>

<script setup>
import MyPerformance from "./components/MyPerformance.vue";
</script>
```

2. **单独使用子组件:**
```vue
<template>
  <div>
    <!-- 时间筛选 -->
    <TimeFilter v-model="timeFilter" @change="handleChange" />
    
    <!-- 图表区域 -->
    <div class="flex gap-20px">
      <PerformanceChart :data="chartData" />
      <PerformancePieChart :data="pieData" />
    </div>
  </div>
</template>
```

## 自定义配置

### 修改图表颜色
在组件内部的 `colors` 数组中修改颜色配置：
```javascript
const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
```

### 修改时间格式
在 `TimeFilter.vue` 的 `timePickerConfig` 中修改格式配置。

### 添加新的筛选维度
在 `dimensionOptions` 数组中添加新的选项，并在 `timePickerConfig` 中添加对应的配置。

## 注意事项

1. 确保已安装并配置了 ECharts 组件
2. API接口需要根据实际后端接口进行调整
3. 模拟数据仅用于开发阶段，生产环境需要替换为真实API调用
4. 组件支持响应式布局，在不同屏幕尺寸下自动适配
