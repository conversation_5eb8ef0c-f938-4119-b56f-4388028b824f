/*
 * @Date         : 2025-07-18
 * @Description  : 我的业绩相关API
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";

// 业绩数据接口参数
export interface PerformanceParams {
  dimension: 'week' | 'month' | 'year';
  time: string;
  orgId?: number;
}

// 业绩数据响应
export interface PerformanceData {
  name: string;
  value: number;
}

// 业绩分类数据响应
export interface PerformanceCategoryData {
  name: string;
  value: number;
  percentage: number;
}

// 业绩概览数据响应
export interface PerformanceOverview {
  currentPerformance: number; // 当前业绩
  targetPerformance: number;  // 目标业绩
  progress: number;           // 完成进度百分比
  ranking: number;            // 排名
}

/**
 * 获取业绩柱状图数据
 */
export const getPerformanceChartData = (params: PerformanceParams) => {
  return http.request<PerformanceData[]>("get", "/api/performance/chart", { params });
};

/**
 * 获取业绩分类饼图数据
 */
export const getPerformanceCategoryData = (params: PerformanceParams) => {
  return http.request<PerformanceCategoryData[]>("get", "/api/performance/category", { params });
};

/**
 * 获取业绩概览数据
 */
export const getPerformanceOverview = (params: PerformanceParams) => {
  return http.request<PerformanceOverview>("get", "/api/performance/overview", { params });
};

// 模拟数据生成函数（开发阶段使用）
export const mockPerformanceChartData = (dimension: string): PerformanceData[] => {
  if (dimension === 'week') {
    return [
      { name: '周一', value: 3135.93 },
      { name: '周二', value: 12000 },
      { name: '周三', value: 2900 },
      { name: '周四', value: 6711.29 },
      { name: '周五', value: 4500 },
      { name: '周六', value: 0 },
      { name: '周日', value: 1600 }
    ];
  } else if (dimension === 'month') {
    return Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 15000)
    }));
  } else {
    return Array.from({ length: 12 }, (_, i) => ({
      name: `${i + 1}月`,
      value: Math.floor(Math.random() * 100000)
    }));
  }
};

export const mockPerformanceCategoryData = (): PerformanceCategoryData[] => {
  return [
    { name: '一年制头条', value: 35, percentage: 35 },
    { name: '升学', value: 20, percentage: 20 },
    { name: '升学提升课程', value: 15, percentage: 15 },
    { name: '短期高效', value: 15, percentage: 15 },
    { name: '其他综合套餐', value: 15, percentage: 15 }
  ];
};

export const mockPerformanceOverview = (): PerformanceOverview => {
  return {
    currentPerformance: 45000,
    targetPerformance: 60000,
    progress: 75,
    ranking: 34
  };
};
