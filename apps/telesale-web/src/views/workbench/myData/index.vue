<!--
 * @Date         : 2025-03-28 16:58:14
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts" name="GroupDataAnalysis">
import CustomerSourceChart from "./components/CustomerSourceChart.vue";
import MyPerformance from "./components/MyPerformance.vue";
import MyPerformanceChat from "./components/MyPerformanceChat.vue";
import MyCallStatistics from "./components/MyCallStatistics.vue";
</script>

<template>
  <div class="g-margin-20">
    <MyPerformance />
    <MyPerformanceChat />
    <CustomerSourceChart />
    <MyCallStatistics />
    <!-- <MonthlyRevenue /> -->
  </div>
</template>

<style lang="scss" scoped>
.el-card {
  margin-top: 10px;
}
</style>
