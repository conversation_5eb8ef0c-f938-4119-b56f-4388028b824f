<script setup lang="ts">
import { ref } from "vue";
import { revenue, consume, revenueRank, rateRank } from "/@/api/home";
import RankTable from "/@/components/RankTable/index.vue";
import { useMultiTagsStoreHook } from "/@/store/modules/multiTags";
import { useRouter } from "vue-router";

const router = useRouter();

function jumpSalary() {
  let params = { beginTime: beginTime.value, endTime: endTime.value };
  // TODO： 跳转函数可以进行封装，此函数应该是对路由导航条的缓存
  useMultiTagsStoreHook().handleTags("push", {
    path: "/statistics/salary/index",
    name: "salary",
    query: params,
    meta: {
      title: "绩效",
      dynamicLevel: 1
    }
  });
  router.push({
    name: "salary",
    query: params
  });
}

const tableHeaderPersonRevenue = [
  { field: "worker", desc: "坐席名称" },
  { field: "amount", desc: "营收" }
];
const tableHeaderGroupRevenue = [
  { field: "name", desc: "小组名称" },
  { field: "amount", desc: "营收" }
];

const tableHeaderPersonRate = [
  { field: "worker", desc: "坐席名称" },
  { field: "rate", desc: "线索转化率" }
];
const tableHeaderGroupRate = [
  { field: "name", desc: "小组名称" },
  { field: "rate", desc: "线索转化率" }
];

const loadingTodayConsume = ref(false);
const loadingTodayRevenue = ref(false);
const loadingRevenue = ref(false);
const loadingRate = ref(false);

const revenueActive = ref(1);
const rateActive = ref(1);
const beginTime = ref();
const endTime = ref();
const getToday = () => {
  let time = new Date();
  let today =
    time.getFullYear() + "/" + (time.getMonth() + 1) + "/" + time.getDate();
  beginTime.value = new Date(today + " 00:00:00").getTime();
  endTime.value = new Date(today + " 23:59:59").getTime();
};
getToday();

let todayRevenue = ref({
  amount: "",
  orderNum: "",
  avgPrice: "",
  convRate: 0
});

// TODO: 使用计算属性
function revenueMath() {
  loadingTodayRevenue.value = true;
  revenue({
    begin: beginTime.value / 1000,
    end: endTime.value / 1000,
    convertRate: true
  })
    .then(({ data }: { data: any }) => {
      todayRevenue.value = data;
      loadingTodayRevenue.value = false;
    })
    .catch(() => {
      loadingTodayRevenue.value = false;
    });
}
revenueMath();

let todayConsume = ref({
  consume: ["", ""],
  pay: ["", ""],
  validCall: ["", ""]
});

// TODO: 使用计算属性
function consumeMath() {
  loadingTodayConsume.value = true;
  consume({ begin: beginTime.value / 1000, end: endTime.value / 1000 })
    .then(({ data }: { data: any }) => {
      todayConsume.value = data;
      loadingTodayConsume.value = false;
    })
    .catch(() => {
      loadingTodayConsume.value = false;
    });
}
consumeMath();

let tableDataPersonRevenue = ref([]);
let tableDataGroupRevenue = ref([]);
function revenueRankMath() {
  loadingRevenue.value = true;
  // TODO：异步函数优先使用await/async
  revenueRank()
    .then(({ data }: { data: any }) => {
      let personList = [],
        groupList = [];
      for (let i = 0; i < 10; i++) {
        let person = { worker: "\xa0", amount: "" };
        let group = { name: "\xa0", amount: "" };
        if (data.amountPerMonth && data.amountPerMonth[i]) {
          person.worker = data.amountPerMonth[i].worker;
          person.amount = data.amountPerMonth[i].amount;
        }
        if (data.groupAmount && data.groupAmount[i]) {
          group.name = data.groupAmount[i]?.name;
          group.amount = data.groupAmount[i].amount;
        }
        personList.push(person);
        groupList.push(group);
      }
      tableDataPersonRevenue.value = personList;
      tableDataGroupRevenue.value = groupList;
      loadingRevenue.value = false;
    })
    .catch(() => {
      // TODO：then、catch都调用相同方法，可以提取到外部，减少重复代码
      loadingRevenue.value = false;
    });
}

// TODO：函数调用放在onMounted生命周期中
revenueRankMath();

let tableDataPersonRate = ref([]);
let tableDataGroupRate = ref([]);
function rateRankMath() {
  loadingRate.value = true;

  // TODO：异步函数优先使用await/async
  rateRank()
    .then(({ data }: { data: any }) => {
      let personList = [],
        groupList = [];
      for (let i = 0; i < 10; i++) {
        let person = { worker: "\xa0", rate: "" };
        let group = { name: "\xa0", rate: "" };
        if (data.worker && data.worker[i]) {
          person.worker = data.worker[i].worker;
          person.rate = data.worker[i].rate + "%";
        }
        if (data.group && data.group[i]) {
          group.name = data.group[i]?.name;
          group.rate = data.group[i].rate + "%";
        }
        personList.push(person);
        groupList.push(group);
      }
      tableDataPersonRate.value = personList;
      tableDataGroupRate.value = groupList;
      loadingRate.value = false;
    })
    .catch(() => {
      loadingRate.value = false;
    });
}

// TODO：函数调用放在onMounted生命周期中
rateRankMath();
</script>

<template>
  <div class="g-margin-20">
    <el-card
      class="g-card g-margin-b-20"
      v-loading="loadingTodayRevenue"
      @click="jumpSalary"
    >
      <div class="g-card__header">今日销售简报</div>
      <IconifyIconOffline class="d-jump" icon="arrow-right" />
      <el-row>
        <el-col :sm="12" :md="6">
          <div class="d-left bg-purple">
            <FontIcon icon="icon-amount" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>销售额（元）</p>
            <span>{{ todayRevenue.amount }}</span>
          </div>
        </el-col>
        <el-col :sm="12" :md="6">
          <div class="d-left bg-blue">
            <FontIcon icon="icon-order" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>订单量（笔）</p>
            <span>{{ todayRevenue.orderNum }}</span>
          </div>
        </el-col>
        <el-col :sm="12" :md="6">
          <div class="d-left bg-green">
            <FontIcon icon="icon-avg" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>客单价（元）</p>
            <span>{{ todayRevenue.avgPrice }}</span>
          </div>
        </el-col>
        <el-col :sm="12" :md="6">
          <div class="d-left-circle">
            <el-progress
              type="circle"
              :percentage="todayRevenue.convRate"
              :width="70"
            />
          </div>
          <div class="d-rit">
            <p>转化率（%）</p>
            <span>{{ todayRevenue.convRate }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="g-card g-margin-b-20" v-loading="loadingTodayConsume">
      <div class="g-card__header">今日线索转化</div>
      <el-row>
        <el-col :sm="12" :md="8">
          <div class="d-left bg-purple">
            <FontIcon icon="icon-used" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>消耗线索（个）</p>
            <span>{{ todayConsume.consume[0] + todayConsume.consume[1] }}</span>
          </div>
          <div class="d-bot">
            <p>
              <span class="d-new" />
              新增：{{ todayConsume.consume[0] }}
            </p>
            <p>
              <span class="d-pay" />
              付费：{{ todayConsume.consume[1] }}
            </p>
          </div>
        </el-col>
        <el-col :sm="12" :md="8">
          <div class="d-left bg-blue">
            <FontIcon icon="icon-call" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>有效沟通线索（个）</p>
            <span>
              {{ todayConsume.validCall[0] + todayConsume.validCall[1] }}
            </span>
          </div>
          <div class="d-bot">
            <p>
              <span class="d-new" />
              新增：{{ todayConsume.validCall[0] }}
            </p>
            <p>
              <span class="d-pay" />
              付费：{{ todayConsume.validCall[1] }}
            </p>
          </div>
        </el-col>
        <el-col :sm="12" :md="8">
          <div class="d-left bg-green">
            <FontIcon icon="icon-clue" svg class="d-iconfont" />
          </div>
          <div class="d-rit">
            <p>成单线索（个）</p>
            <span>{{ todayConsume.pay[0] + todayConsume.pay[1] }}</span>
          </div>
          <div class="d-bot">
            <p>
              <span class="d-new" />
              新增：{{ todayConsume.pay[0] }}
            </p>
            <p>
              <span class="d-pay" />
              付费：{{ todayConsume.pay[1] }}
            </p>
          </div>
        </el-col>
      </el-row>
    </el-card>
    <el-row :gutter="20">
      <el-col :md="12">
        <el-card class="g-card g-margin-b-20" v-loading="loadingRevenue">
          <div class="g-card__header">本月营收排行榜</div>
          <el-tabs type="border-card" v-model="revenueActive">
            <el-tab-pane label="个人" :name="1">
              <RankTable
                :tableData="tableDataPersonRevenue"
                :tableHeader="tableHeaderPersonRevenue"
              />
            </el-tab-pane>
            <el-tab-pane label="小组" :name="2">
              <RankTable
                :tableData="tableDataGroupRevenue"
                :tableHeader="tableHeaderGroupRevenue"
              />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
      <el-col :md="12">
        <el-card class="g-card g-margin-b-20" v-loading="loadingRate">
          <div class="g-card__header">本月转化率排行榜</div>
          <el-tabs type="border-card" v-model="rateActive">
            <el-tab-pane label="个人" :name="1">
              <RankTable
                :tableData="tableDataPersonRate"
                :tableHeader="tableHeaderPersonRate"
              />
            </el-tab-pane>
            <el-tab-pane label="小组" :name="2">
              <RankTable
                :tableData="tableDataGroupRate"
                :tableHeader="tableHeaderGroupRate"
              />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<style lang="scss" scoped>
// TODO:页面级样式放在本页，style文件夹下的样式应是全局样式
@import "/@/style/home.scss";
</style>
