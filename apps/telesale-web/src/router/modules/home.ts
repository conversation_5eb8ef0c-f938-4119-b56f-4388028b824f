const Layout = () => import("/@/layout/index.vue");

const homeRouter = {
  path: "/",
  name: "home",
  component: Layout,
  redirect: "/index",
  meta: {
    icon: "home-filled",
    title: "个人工作台"
  },
  children: [
    {
      path: "/index",
      name: "index",
      component: () => import("/@/views/workbench/home/<USER>"),
      meta: {
        title: "首页"
      }
    },
    {
      path: "/myData/index",
      name: "MyData",
      component: () => import("/@/views/workbench/myData/index.vue"),
      meta: {
        title: "我的数据"
      }
    }
  ]
};

export default homeRouter;
