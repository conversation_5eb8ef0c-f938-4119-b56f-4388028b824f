import { dayjs } from 'element-plus'

export default function () {
  const shortcuts = [
    {
      text: '今天',
      value() {
        return [dayjs().startOf('D'), dayjs().endOf('D')]
      },
    },
    {
      text: '昨天',
      value() {
        return [dayjs().subtract(1, 'day').startOf('D'), dayjs().subtract(1, 'day').endOf('D')]
      },
    },
    {
      text: '最近7天',
      value() {
        return [dayjs().subtract(6, 'day').startOf('D'), dayjs().endOf('D')]
      },
    },
    {
      text: '过去7天',
      value() {
        return [dayjs().subtract(7, 'day').startOf('D'), dayjs().subtract(1, 'day').endOf('D')]
      },
    },
    {
      text: '过去30天',
      value() {
        return [dayjs().subtract(30, 'day').startOf('D'), dayjs().subtract(1, 'day').endOf('D')]
      },
    },
  ]

  return { shortcuts }
}
