import { dayjs } from 'element-plus'

export default function(props, {
  startTime
}) {

  const defaultTime: [Date, Date] = [new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)] // 当日具体时刻 00:00:00 - 23:59:59

  function disabledDate(time: Date, date, type) { // 计算哪些日期是禁用的
    const compareTime = dayjs(time) // 输入的日期
    const currentTime = dayjs() // 当天
    const outTimePick = type === 'paramast' ? compareTime.isAfter(currentTime, 'day') : compareTime.isBefore(currentTime, 'day')

    if (!date) {
      return outTimePick
    }

    let beforeTimePick = false
    let afterTimePick = false
    if (startTime.value) {
      beforeTimePick = compareTime.isBefore(dayjs(startTime.value).subtract(date.num, date.type))
      afterTimePick = compareTime.isAfter(dayjs(startTime.value).add(date.num, date.type))
    }

    return outTimePick || afterTimePick || beforeTimePick
  }

  function disabledDateHandle(time) {
    if (props.limitType === 'none') {
      return false
    }

    return disabledDate(time, props.limitDate, props.limitType)
  }

  return { disabledDateHandle, defaultTime }
}
