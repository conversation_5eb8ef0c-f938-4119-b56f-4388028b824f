import { ref } from 'vue'

export function useHeaderEdit() {
  const HeaderEditRef = ref()
  const headerEditVisible = ref(false)

  const showHeaderKeyList = ref<string[]>([])

  function checkShow(key: string) {
    return showHeaderKeyList.value.includes(key)
  }

  return { HeaderEditRef, headerEditVisible, checkShow, showHeaderKeyList }
}

export interface HeaderEditConfig {
  localKey: string
  localList: {
    label: string
    key: string
    defaultUnShow?: boolean
  }[]
}
