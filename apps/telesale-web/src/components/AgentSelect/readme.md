# 地区选择组件

## 介绍
用于选择坐席
## 引入

```js
import AgentSelect from '/@/components/AgentSelect/index';
```

## 基础用法

```tsx
<teplate>
  <AgentSelect v-model="searchForm.workerId" />
</teplate>

<script lang="ts" setup>
  import { ref } from 'vue';
  import AgentSelect from '/@/components/CitySelector/index';

  const searchForm = ref({
    workerId: undefined
  });
</script>
```

## Api

### Props

| 参数            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| modelValue  | 双向邦定的value值                                                    | number / undefined |  undefined   |
| isAll         |  可选项是否是所有坐席（是否不受组织架构限制）   | boolean |   false  |
